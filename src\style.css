body {
    font-family: Arial, sans-serif;
    background: #f9f9f9;
    margin: 0;
    padding: 20px;
}

.container {
    background: #fff;
    margin: 2em auto;
    padding: 2em;
    border-radius: 8px;
    max-width: 600px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

h1 {
    text-align: center;
    color: #333;
    margin-bottom: 1em;
}

h2 {
    color: #555;
    font-size: 1.2em;
    margin: 1em 0 0.5em;
}

/* Forms */
.form-container,
.tasks-container,
.subscription-container {
    margin-bottom: 2em;
}

form {
    display: flex;
    margin-bottom: 1em;
}

input[type="text"],
input[type="email"] {
    flex: 1;
    padding: 0.7em;
    border: 1px solid #ddd;
    border-radius: 4px 0 0 4px;
    font-size: 1em;
}

button {
    background: #4a90e2;
    color: white;
    border: none;
    padding: 0.7em 1.2em;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
    font-size: 1em;
}

button:hover {
    background: #357ae8;
}

/* Tasks */
#tasks-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.task-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.8em 0;
    border-bottom: 1px solid #eee;
}

.task-item .task-name {
    margin-left: 0.5em;
    flex: 1;
}

.task-item.completed .task-name {
    text-decoration: line-through;
    color: #888;
}

.task-status {
    margin: 0;
    cursor: pointer;
}

.toggle-form,
.delete-form {
    display: flex;
    align-items: center;
}

.toggle-form {
    flex: 1;
}

/* Delete button */
.delete-task {
    background: #ff6b6b;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 0.4em 0.8em;
    cursor: pointer;
    font-size: 0.9em;
    margin-left: 1em;
}

.delete-task:hover {
    background: #ff5252;
}

/* Messages */
.message {
    padding: 0.8em 1em;
    margin-bottom: 1.5em;
    border-radius: 4px;
}

.message.success {
    background: #e0ffe0;
    color: #2d7a2d;
    border-left: 4px solid #4caf50;
}

.message.error {
    background: #ffe0e0;
    color: #a12d2d;
    border-left: 4px solid #f44336;
}

/* Responsive */
@media (max-width: 600px) {
    .container {
        margin: 1em;
        padding: 1em;
    }

    form {
        flex-direction: column;
    }

    input[type="text"],
    input[type="email"] {
        border-radius: 4px;
        margin-bottom: 0.5em;
    }

    button {
        border-radius: 4px;
    }
}