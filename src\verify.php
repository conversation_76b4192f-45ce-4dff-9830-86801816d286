<?php
require_once 'functions.php';

$message = '';
$status = '';

// Check if email and verification code are provided
if (isset($_GET['email']) && isset($_GET['code'])) {
    $email = $_GET['email'];
    $code = $_GET['code'];
    
    // Attempt to verify the subscription
    if (verifySubscription($email, $code)) {
        $message = "Your email subscription has been successfully verified!";
        $status = 'success';
    } else {
        $message = "Verification failed. The code may be invalid or expired.";
        $status = 'error';
    }
} else {
    $message = "Missing email or verification code.";
    $status = 'error';
}
?>

<!DOCTYPE html>
<html>

<head>
    <title>Subscription Verification</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="style.css">
</head>

<body>
    <div class="container">
        <!-- Do not modify the ID of the heading -->
        <h2 id="verification-heading">Subscription Verification</h2>
        
        <?php if (!empty($message)): ?>
            <div class="message <?php echo $status; ?>"><?php echo htmlspecialchars($message); ?></div>
        <?php endif; ?>
        
        <p>You will now receive task reminders hourly for any pending tasks.</p>
        
        <div class="navigation-links">
            <a href="index.php">Return to Task Scheduler</a>
        </div>
    </div>
</body>

</html>