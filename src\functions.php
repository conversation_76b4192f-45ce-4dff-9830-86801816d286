<?php

/**
 * Adds a new task to the task list
 * 
 * @param string $task_name The name of the task to add.
 * @return bool True on success, false on failure.
 */
function addTask(string $task_name): bool
{
	$file = __DIR__ . '/tasks.txt';
	$tasks = read_json_file($file);
	$task_name_trimmed = trim($task_name);
	if ($task_name_trimmed === '') return false;
	foreach ($tasks as $task) {
		if (strtolower($task['name']) === strtolower($task_name_trimmed)) return false;
	}
	$tasks[] = [
		'id' => uniqid('', true),
		'name' => $task_name_trimmed,
		'completed' => false
	];
	return write_json_file($file, $tasks);
}

/**
 * Retrieves all tasks from the tasks.txt file
 * 
 * @return array Array of tasks. -- Format [ id, name, completed ]
 */
function getAllTasks(): array
{
	$file = __DIR__ . '/tasks.txt';
	return read_json_file($file);
}

/**
 * Marks a task as completed or uncompleted
 * 
 * @param string $task_id The ID of the task to mark.
 * @param bool $is_completed True to mark as completed, false to mark as uncompleted.
 * @return bool True on success, false on failure
 */
function markTaskAsCompleted(string $task_id, bool $is_completed): bool
{
	$file = __DIR__ . '/tasks.txt';
	$tasks = read_json_file($file);
	$updated = false;
	foreach ($tasks as &$task) {
		if ($task['id'] === $task_id) {
			$task['completed'] = (bool)$is_completed;
			$updated = true;
		}
	}
	unset($task);
	return $updated ? write_json_file($file, $tasks) : false;
}

/**
 * Deletes a task from the task list
 * 
 * @param string $task_id The ID of the task to delete.
 * @return bool True on success, false on failure.
 */
function deleteTask(string $task_id): bool
{
	$file = __DIR__ . '/tasks.txt';
	$tasks = read_json_file($file);
	$new_tasks = array_filter($tasks, function($task) use ($task_id) {
		return $task['id'] !== $task_id;
	});
	if (count($tasks) === count($new_tasks)) return false;
	return write_json_file($file, array_values($new_tasks));
}

/**
 * Generates a 6-digit verification code
 * 
 * @return string The generated verification code.
 */
function generateVerificationCode(): string
{
	return str_pad(strval(random_int(0, 999999)), 6, '0', STR_PAD_LEFT);
}

/**
 * Subscribe an email address to task notifications.
 *
 * Generates a verification code, stores the pending subscription,
 * and sends a verification email to the subscriber.
 *
 * @param string $email The email address to subscribe.
 * @return bool True if verification email sent successfully, false otherwise.
 */
function subscribeEmail(string $email): bool
{
	$pending_file = __DIR__ . '/pending_subscriptions.txt';
	$subs_file = __DIR__ . '/subscribers.txt';
	$email = strtolower(trim($email));
	if (!filter_var($email, FILTER_VALIDATE_EMAIL)) return false;
	$subs = read_json_file($subs_file);
	if (in_array($email, $subs)) return false;
	$pending = read_json_file($pending_file);
	foreach ($pending as $entry) {
		if ($entry['email'] === $email) return false;
	}
	$code = generateVerificationCode();
	$pending[] = ['email' => $email, 'code' => $code];
	if (!write_json_file($pending_file, $pending)) return false;
	// Generate verification URL - handle both web and cron contexts
	$base_url = '';
	if (isset($_SERVER['HTTP_HOST']) && isset($_SERVER['PHP_SELF'])) {
		$base_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']);
	} else {
		// Fallback for cron context - you may need to adjust this URL
		$base_url = 'http://localhost:8000';
	}
	$verify_url = $base_url . '/verify.php?email=' . urlencode($email) . '&code=' . urlencode($code);
	$subject = 'Verify subscription to Task Planner';
	$body = '<p>Click the link below to verify your subscription to Task Planner:</p>';
	$body .= '<p><a id="verification-link" href="' . $verify_url . '">Verify Subscription</a></p>';
	$headers = "MIME-Version: 1.0\r\n";
	$headers .= "Content-type: text/html; charset=UTF-8\r\n";
	$headers .= "From: <EMAIL>\r\n";
	return mail($email, $subject, $body, $headers);
}

/**
 * Verifies an email subscription
 * 
 * @param string $email The email address to verify.
 * @param string $code The verification code.
 * @return bool True on success, false on failure.
 */
function verifySubscription(string $email, string $code): bool
{
	$pending_file = __DIR__ . '/pending_subscriptions.txt';
	$subs_file = __DIR__ . '/subscribers.txt';
	$email = strtolower(trim($email));
	$pending = read_json_file($pending_file);
	$subs = read_json_file($subs_file);
	$found = false;
	$new_pending = [];
	foreach ($pending as $entry) {
		if ($entry['email'] === $email && $entry['code'] === $code) {
			$found = true;
			if (!in_array($email, $subs)) $subs[] = $email;
		} else {
			$new_pending[] = $entry;
		}
	}
	if ($found) {
		write_json_file($pending_file, $new_pending);
		write_json_file($subs_file, $subs);
		return true;
	}
	return false;
}

/**
 * Unsubscribes an email from the subscribers list
 * 
 * @param string $email The email address to unsubscribe.
 * @return bool True on success, false on failure.
 */
function unsubscribeEmail(string $email): bool
{
	$subs_file = __DIR__ . '/subscribers.txt';
	$email = strtolower(trim($email));
	$subs = read_json_file($subs_file);
	$new_subs = array_filter($subs, function($e) use ($email) { return $e !== $email; });
	if (count($subs) === count($new_subs)) return false;
	return write_json_file($subs_file, array_values($new_subs));
}

/**
 * Sends task reminders to all subscribers
 * Internally calls sendTaskEmail() for each subscriber
 */
function sendTaskReminders(): void
{
	$subs_file = __DIR__ . '/subscribers.txt';
	$subs = read_json_file($subs_file);
	$tasks = getAllTasks();
	$pending_tasks = array_filter($tasks, function($task) { return !$task['completed']; });
	if (empty($pending_tasks) || empty($subs)) return;
	foreach ($subs as $email) {
		sendTaskEmail($email, $pending_tasks);
	}
}

/**
 * Sends a task reminder email to a subscriber with pending tasks.
 *
 * @param string $email The email address of the subscriber.
 * @param array $pending_tasks Array of pending tasks to include in the email.
 * @return bool True if email was sent successfully, false otherwise.
 */
function sendTaskEmail(string $email, array $pending_tasks): bool
{
	$subject = 'Task Planner - Pending Tasks Reminder';
	$body = '<h2>Pending Tasks Reminder</h2>';
	$body .= '<p>Here are the current pending tasks:</p>';
	$body .= '<ul>';
	foreach ($pending_tasks as $task) {
		$body .= '<li>' . htmlspecialchars($task['name']) . '</li>';
	}
	$body .= '</ul>';
	// Generate unsubscribe URL - handle both web and cron contexts
	$base_url = '';
	if (isset($_SERVER['HTTP_HOST']) && isset($_SERVER['PHP_SELF'])) {
		$base_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']);
	} else {
		// Fallback for cron context - you may need to adjust this URL
		$base_url = 'http://localhost:8000';
	}
	$unsubscribe_url = $base_url . '/unsubscribe.php?email=' . urlencode($email);
	$body .= '<p><a id="unsubscribe-link" href="' . $unsubscribe_url . '">Unsubscribe from notifications</a></p>';
	$headers = "MIME-Version: 1.0\r\n";
	$headers .= "Content-type: text/html; charset=UTF-8\r\n";
	$headers .= "From: <EMAIL>\r\n";
	return mail($email, $subject, $body, $headers);
}

// Helper: Read JSON file
function read_json_file($file, $default = []) {
	if (!file_exists($file)) return $default;
	$data = file_get_contents($file);
	$json = json_decode($data, true);
	return is_array($json) ? $json : $default;
}

// Helper: Write JSON file
function write_json_file($file, $data) {
	return file_put_contents($file, json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE), LOCK_EX) !== false;
}
