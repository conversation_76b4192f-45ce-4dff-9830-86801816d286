#!/bin/bash

# Task Scheduler CRON Job Setup Script
# This script sets up a CRON job to run cron.php every hour

echo "Setting up CRON job for Task Scheduler..."

# Get the current directory
CURRENT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CRON_FILE="$CURRENT_DIR/cron.php"

# Check if cron.php exists
if [ ! -f "$CRON_FILE" ]; then
    echo "Error: cron.php not found in $CURRENT_DIR"
    exit 1
fi

# Create the cron job entry
# Run every hour at minute 0
CRON_ENTRY="0 * * * * /usr/bin/php \"$CRON_FILE\""

# Check if crontab command exists
if ! command -v crontab &> /dev/null; then
    echo "Error: crontab command not found. Please install cron."
    echo "For Windows users, consider using Task Scheduler instead:"
    echo "1. Open Task Scheduler"
    echo "2. Create Basic Task"
    echo "3. Set trigger to 'Daily' and repeat every 1 hour"
    echo "4. Set action to start program: php.exe"
    echo "5. Add argument: \"$CRON_FILE\""
    exit 1
fi

# Backup existing crontab
echo "Backing up existing crontab..."
crontab -l > /tmp/crontab_backup_$(date +%Y%m%d_%H%M%S) 2>/dev/null || true

# Add the new cron job (avoid duplicates)
echo "Adding CRON job..."
(crontab -l 2>/dev/null | grep -v "$CRON_FILE"; echo "$CRON_ENTRY") | crontab -

if [ $? -eq 0 ]; then
    echo "CRON job successfully added!"
    echo "The task reminder system will run every hour."
    echo ""
    echo "Current crontab:"
    crontab -l
else
    echo "Error: Failed to add CRON job."
    exit 1
fi

echo ""
echo "Setup complete! The system will now send hourly task reminders to subscribers."
echo "To remove the CRON job later, run: crontab -e"