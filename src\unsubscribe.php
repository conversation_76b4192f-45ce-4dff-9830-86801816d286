<?php
require_once 'functions.php';

$message = '';
$status = '';

// Check if email is provided
if (isset($_GET['email'])) {
    $email = $_GET['email'];
    
    // Attempt to unsubscribe the email
    if (unsubscribeEmail($email)) {
        $message = "You have been successfully unsubscribed from task reminders.";
        $status = 'success';
    } else {
        $message = "Unsubscription failed. The email may not be in our subscriber list.";
        $status = 'error';
    }
} else {
    $message = "Missing email address.";
    $status = 'error';
}
?>

<!DOCTYPE html>
<html>

<head>
    <title>Unsubscribe from Task Updates</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="style.css">
</head>

<body>
    <div class="container">
        <!-- Do not modify the ID of the heading -->
        <h2 id="unsubscription-heading">Unsubscribe from Task Updates</h2>
        
        <?php if (!empty($message)): ?>
            <div class="message <?php echo $status; ?>"><?php echo htmlspecialchars($message); ?></div>
        <?php endif; ?>
        
        <p>You will no longer receive task reminders from our system.</p>
        
        <div class="navigation-links">
            <a href="index.php">Return to Task Scheduler</a>
        </div>
    </div>
</body>

</html>