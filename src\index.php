<?php
require_once 'functions.php';

// Initialize variables for messages
$message = '';
$email_message = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
	// Add task form submission
	if (isset($_POST['task-name']) && !empty($_POST['task-name'])) {
		$task_name = trim($_POST['task-name']);
		if (addTask($task_name)) {
			$message = "Task added successfully!";
		} else {
			$message = "Failed to add task.";
		}
	}

	// Email subscription form submission
	if (isset($_POST['email']) && !empty($_POST['email'])) {
		$email = filter_var($_POST['email'], FILTER_VALIDATE_EMAIL);
		if ($email) {
			if (subscribeEmail($email)) {
				$email_message = "Verification email sent. Please check your inbox.";
			} else {
				$email_message = "Failed to subscribe. Please try again.";
			}
		} else {
			$email_message = "Invalid email address.";
		}
	}

	// Toggle task completion status
	if (isset($_POST['toggle-task']) && isset($_POST['task-id'])) {
		$task_id = $_POST['task-id'];
		$is_completed = isset($_POST['is-completed']) ? (bool)$_POST['is-completed'] : false;
		if (markTaskAsCompleted($task_id, $is_completed)) {
			$message = "Task status updated.";
		} else {
			$message = "Failed to update task status.";
		}
	}

	// Delete task
	if (isset($_POST['delete-task']) && isset($_POST['task-id'])) {
		$task_id = $_POST['task-id'];
		if (deleteTask($task_id)) {
			$message = "Task deleted successfully.";
		} else {
			$message = "Failed to delete task.";
		}
	}
}

// Get all tasks
$tasks = getAllTasks();
?>
<!DOCTYPE html>
<html>

<head>
	<title>Task Scheduler</title>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<link rel="stylesheet" href="style.css">
</head>

<body>
	<div class="container">
		<h1>Task Scheduler</h1>

		<?php if (!empty($message)): ?>
			<div class="message <?php echo strpos($message, 'Failed') !== false ? 'error' : 'success'; ?>"><?php echo htmlspecialchars($message); ?></div>
		<?php endif; ?>

		<!-- Add Task Form -->
		<div class="form-container">
			<h2>Add New Task</h2>
			<form method="POST" action="">
				<input type="text" name="task-name" id="task-name" placeholder="Enter new task" required>
				<button type="submit" id="add-task">Add Task</button>
			</form>
		</div>

		<!-- Tasks List -->
		<div class="tasks-container">
			<h2>Your Tasks</h2>
			<?php if (empty($tasks)): ?>
				<p>No tasks yet. Add your first task above!</p>
			<?php else: ?>
				<ul id="tasks-list">
					<?php foreach ($tasks as $task): ?>
						<li class="task-item <?php echo $task['completed'] ? 'completed' : ''; ?>">
							<form method="POST" action="" class="toggle-form">
								<input type="hidden" name="task-id" value="<?php echo htmlspecialchars($task['id']); ?>">
								<input type="hidden" name="is-completed" value="<?php echo $task['completed'] ? '0' : '1'; ?>">
								<input type="checkbox" class="task-status" <?php echo $task['completed'] ? 'checked' : ''; ?> onchange="this.form.submit()" name="toggle-task">
								<span class="task-name"><?php echo htmlspecialchars($task['name']); ?></span>
							</form>
							<form method="POST" action="" class="delete-form">
								<input type="hidden" name="task-id" value="<?php echo htmlspecialchars($task['id']); ?>">
								<button type="submit" class="delete-task" name="delete-task">Delete</button>
							</form>
						</li>
					<?php endforeach; ?>
				</ul>
			<?php endif; ?>
		</div>

		<!-- Subscription Form -->
		<div class="subscription-container">
			<h2>Subscribe to Task Reminders</h2>
			<?php if (!empty($email_message)): ?>
				<div class="message <?php echo strpos($email_message, 'Failed') !== false || strpos($email_message, 'Invalid') !== false ? 'error' : 'success'; ?>"><?php echo htmlspecialchars($email_message); ?></div>
			<?php endif; ?>
			<form method="POST" action="">
				<input type="email" name="email" id="email" placeholder="Enter your email" required>
				<button type="submit" id="submit-email">Subscribe</button>
			</form>
		</div>
	</div>
</body>

</html>